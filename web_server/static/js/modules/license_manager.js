// License Manager for Crucible
// Handles license validation and enforcement

class LicenseManager {
    constructor() {
        this.isLicensed = false;
        this.licenseInfo = null;
        this.checkInProgress = false;
    }

    // Initialize license manager
    init() {
        this.setupEventListeners();
        this.checkLicenseStatus();
        this.enforceLicenseRestrictions();
        console.log('License Manager initialized');
    }

    // Setup event listeners for license-related buttons
    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.id === 'check-license-btn') this.checkLicenseStatus();
            if (e.target.id === 'purchase-license-btn') this.openPurchasePage();
            if (e.target.id === 'enter-license-btn') this.openLicenseKeyModal();
            if (e.target.id === 'validate-license-btn') this.validateLicenseKey();
        });

        // Format license key input
        const licenseInput = document.getElementById('license-key-input');
        if (licenseInput) {
            licenseInput.addEventListener('input', this.formatLicenseKey);
            licenseInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') this.validateLicenseKey();
            });
        }
    }

    // Check current license status
    async checkLicenseStatus() {
        if (this.checkInProgress) return;
        
        this.checkInProgress = true;
        this.showLicenseMessage('Checking license status...', 'info');

        try {
            // Try to get license status from Nexus API
            const response = await fetch('/api/license/status');
            
            if (response.ok) {
                const data = await response.json();
                this.handleLicenseResponse(data);
            } else if (response.status === 401) {
                // No API key configured - check for local license file
                this.checkLocalLicense();
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.log('License check failed:', error);
            this.checkLocalLicense();
        } finally {
            this.checkInProgress = false;
        }
    }

    // Check for local license file (offline mode)
    async checkLocalLicense() {
        try {
            const response = await fetch('/api/license/local');
            if (response.ok) {
                const data = await response.json();
                this.handleLicenseResponse(data);
            } else {
                this.setUnlicensed('No valid license found');
            }
        } catch (error) {
            this.setUnlicensed('Unable to verify license');
        }
    }

    // Handle license response from server
    handleLicenseResponse(data) {
        if (data.valid && data.license) {
            this.setLicensed(data.license);
        } else {
            this.setUnlicensed(data.message || 'Invalid license');
        }
    }

    // Set licensed state
    setLicensed(licenseInfo) {
        this.isLicensed = true;
        this.licenseInfo = licenseInfo;
        
        // Hide license banner
        const banner = document.getElementById('license-status-banner');
        if (banner) banner.style.display = 'none';
        
        // Enable all functionality
        this.enableAllFeatures();
        
        this.showLicenseMessage(`License valid: ${licenseInfo.type || 'Licensed'}`, 'success');
        console.log('License validated successfully:', licenseInfo);
    }

    // Set unlicensed state
    setUnlicensed(message) {
        this.isLicensed = false;
        this.licenseInfo = null;
        
        // Show license banner
        const banner = document.getElementById('license-status-banner');
        if (banner) banner.style.display = 'block';
        
        // Disable restricted functionality
        this.enforceLicenseRestrictions();
        
        this.showLicenseMessage(message, 'error');
        console.log('License validation failed:', message);
    }

    // Enforce license restrictions
    enforceLicenseRestrictions() {
        if (this.isLicensed) return;

        // Disable test execution buttons
        const testButtons = [
            'run-tests-btn',
            'run-tests-btn-quick'
        ];
        
        testButtons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.disabled = true;
                btn.title = 'License required to run tests';
                btn.style.opacity = '0.5';
            }
        });

        // Disable drive wipe functionality
        const wipeButtons = [
            'start-wipe-btn',
            'load-drives-btn'
        ];
        
        wipeButtons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.disabled = true;
                btn.title = 'License required for drive operations';
                btn.style.opacity = '0.5';
            }
        });

        // Add click interceptors for additional protection
        this.addClickInterceptors();
    }

    // Enable all features when licensed
    enableAllFeatures() {
        // Enable test execution buttons
        const testButtons = [
            'run-tests-btn',
            'run-tests-btn-quick'
        ];
        
        testButtons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.disabled = false;
                btn.title = '';
                btn.style.opacity = '1';
            }
        });

        // Enable drive wipe functionality
        const wipeButtons = [
            'start-wipe-btn',
            'load-drives-btn'
        ];
        
        wipeButtons.forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.disabled = false;
                btn.title = '';
                btn.style.opacity = '1';
            }
        });

        // Remove click interceptors
        this.removeClickInterceptors();
    }

    // Add click interceptors to prevent unlicensed usage
    addClickInterceptors() {
        document.addEventListener('click', this.licenseClickHandler, true);
    }

    // Remove click interceptors
    removeClickInterceptors() {
        document.removeEventListener('click', this.licenseClickHandler, true);
    }

    // Handle clicks on restricted elements
    licenseClickHandler = (e) => {
        const restrictedIds = [
            'run-tests-btn',
            'run-tests-btn-quick',
            'start-wipe-btn',
            'load-drives-btn'
        ];

        if (restrictedIds.includes(e.target.id)) {
            e.preventDefault();
            e.stopPropagation();
            this.showLicenseRequiredDialog();
            return false;
        }
    }

    // Show license required dialog
    showLicenseRequiredDialog() {
        alert('A valid license is required to use this feature.\n\nPlease enter your license key or purchase a license to continue.');
        this.openLicenseKeyModal();
    }

    // Open license key entry modal
    openLicenseKeyModal() {
        const modal = document.getElementById('license-key-modal');
        if (modal) {
            modal.style.display = 'block';
            const input = document.getElementById('license-key-input');
            if (input) {
                input.focus();
                input.value = '';
            }
            this.hideLicenseMessage();
        }
    }

    // Close license key modal
    closeLicenseKeyModal() {
        const modal = document.getElementById('license-key-modal');
        if (modal) modal.style.display = 'none';
        this.hideLicenseMessage();
    }

    // Format license key input (XXXX-XXXX-XXXX-XXXX)
    formatLicenseKey(e) {
        let value = e.target.value.replace(/[^A-Z0-9]/g, '');
        let formatted = '';
        
        for (let i = 0; i < value.length; i++) {
            if (i > 0 && i % 4 === 0) {
                formatted += '-';
            }
            formatted += value[i];
        }
        
        e.target.value = formatted;
    }

    // Validate entered license key
    async validateLicenseKey() {
        const input = document.getElementById('license-key-input');
        const licenseKey = input?.value?.trim();
        
        if (!licenseKey) {
            this.showLicenseMessage('Please enter a license key', 'error');
            return;
        }

        if (licenseKey.length !== 19) {
            this.showLicenseMessage('License key must be in format XXXX-XXXX-XXXX-XXXX', 'error');
            return;
        }

        this.showLicenseMessage('Validating license key...', 'info');

        try {
            const response = await fetch('/api/license/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ licenseKey })
            });

            const data = await response.json();
            
            if (response.ok && data.valid) {
                this.setLicensed(data.license);
                this.closeLicenseKeyModal();
                this.showLicenseMessage('License activated successfully!', 'success');
            } else {
                this.showLicenseMessage(data.message || 'Invalid license key', 'error');
            }
        } catch (error) {
            this.showLicenseMessage('Failed to validate license key', 'error');
            console.error('License validation error:', error);
        }
    }

    // Open purchase page
    openPurchasePage() {
        window.open('https://nexus.example.com/purchase', '_blank');
    }

    // Show license status message
    showLicenseMessage(message, type) {
        const messageEl = document.getElementById('license-validation-message');
        if (messageEl) {
            messageEl.textContent = message;
            messageEl.className = `status-message ${type}`;
            messageEl.style.display = 'block';
        }
    }

    // Hide license message
    hideLicenseMessage() {
        const messageEl = document.getElementById('license-validation-message');
        if (messageEl) {
            messageEl.style.display = 'none';
        }
    }

    // Check if current operation is allowed
    isOperationAllowed(operation) {
        if (this.isLicensed) return true;
        
        const restrictedOperations = ['test', 'wipe', 'diagnostics'];
        return !restrictedOperations.includes(operation);
    }
}

// Create and export singleton instance
const licenseManager = new LicenseManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => licenseManager.init());
} else {
    licenseManager.init();
}

// Export for use in other modules
window.licenseManager = licenseManager;

// Global functions for modal
window.closeLicenseKeyModal = () => licenseManager.closeLicenseKeyModal();
