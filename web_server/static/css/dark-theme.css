/* Dark Theme for Crucible */

body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
}

header {
    background-color: #1f1f1f;
    color: #ffffff;
    padding: 1rem;
    border-bottom: 1px solid #333;
    font-size: 0.5rem;
}

/* Asset Header Styling - Compact */
.asset-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 0.5rem;
    padding: 0.75rem;
    background-color: #2a2a2a;
    border-radius: 6px;
    border: 1px solid #5565f7;
}

.asset-input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.asset-input-group label {
    font-size: 0.8rem;
    font-weight: bold;
    color: #5565f7;
    margin-bottom: 0;
}

.asset-input-group input {
    width: 140px;
    padding: 8px;
    font-size: 0.95rem;
    text-align: center;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.asset-input-group input:focus {
    border-color: #5565f7;
    outline: none;
}

.asset-status {
    display: flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    background-color: #333;
    min-width: 160px;
    justify-content: center;
    font-size: 0.9rem;
}

.asset-status.ready {
    background-color: #1e4d1e;
    color: #4CAF50;
}

.asset-status.incomplete {
    background-color: #4d1e1e;
    color: #f44336;
}

main {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Grid Layout - Consistent sizing */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.secondary-actions {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.dashboard-card {
    background-color: #1e1e1e;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #333;
    transition: border-color 0.3s ease;
    min-height: 140px;
    display: flex;
    flex-direction: column;
}

.dashboard-card:hover {
    border-color: #555;
}

.priority-card {
    border-color: #5565f7;
    background-color: #1e1e2e;
}

/* Secure Drive Wipe – visual enhancements */
#drive-wipe-section {
    border: 1px solid #2a2f44;
    background: #161823;
    border-left: 5px solid #e24a90; /* accent similar to disks color */
    box-shadow: 0 4px 14px rgba(0,0,0,0.4);
}

#drive-wipe-section .collapsible-header {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 1.05rem;
    color: #ffd0e3;
}

#drive-wipe-section .collapsible-header .collapse-icon {
    color: #e24a90;
}

.drive-wipe-controls {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 0.6rem;
    align-items: center;
    background: #1b1e2b;
    border: 1px solid #2c3150;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.wipe-method-select {
    background-color: #202434;
    color: #e6e6e6;
    border: 1px solid #3b4772;
    border-radius: 6px;
    padding: 0.45rem 0.5rem;
    min-width: 220px;
}

/* Action buttons with distinct intent colors */
#load-drives-btn.btn-secondary {
    background-color: #666;
}
#load-drives-btn.btn-secondary:hover {
    background-color: #888;
}

#start-wipe-btn.btn-danger {
    background-color: #b53242;
    font-weight: 700;
    border: 1px solid #f16a7a66;
}
#start-wipe-btn.btn-danger:hover {
    background-color: #dc3545;
}

#cancel-wipe-btn.btn-warning {
    background-color: #b07a32;
    color: #1c1305;
    font-weight: 700;
    border: 1px solid #ffb74d55;
}
#cancel-wipe-btn.btn-warning:hover {
    background-color: #ff9800;
}

/* Drive list container styled like a panel */
.drive-list-container {
    background: #151827;
    border: 1px solid #2a2f44;
    border-radius: 8px;
    padding: 0.75rem;
    min-height: 64px;
}

.drive-list-container p {
    color: #9aa4c7;
    margin: 0.25rem 0 0;
}

/* Progress bar refresh */
.progress-bar-container {
    background: #1b1e2b;
    border: 1px solid #2a2f44;
    border-radius: 999px;
    overflow: hidden;
    height: 18px;
}

.progress-bar {
    background: linear-gradient(90deg, #4ae287, #90e24a);
    color: #0f1a12;
    font-weight: 700;
    text-shadow: none;
    border-radius: 999px;
}

/* Status message and wipe log */
#wipe-status-message {
    color: #cfe1ff;
    margin-top: 0.5rem;
}

#wipe-log-output.log-output {
    background-color: #10131f;
    border: 1px solid #2a2f44;
    border-radius: 8px;
    max-height: 260px;
}

/* Expanded state */
#drive-wipe-section.collapsible.expanded {
    border-left-color: #ff6aa9;
}

.full-width {
    grid-column: 1 / -1;
}

/* Collapsible Sections */
.collapsible .collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible.expanded .collapsible-content {
    max-height: 500px;
}

.collapsible-header {
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.collapse-icon {
    transition: transform 0.3s ease;
}

.collapsible.expanded .collapse-icon {
    transform: rotate(180deg);
}

h2 {
    color: #5565f7;
    border-bottom: 2px solid #5565f7;
    padding-bottom: 0.5rem;
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #e0e0e0;
}

/* Input Styling */
input[type='text'],
textarea,
select {
    width: calc(100% - 20px);
    padding: 10px;
    margin-bottom: 1rem;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #555;
    border-radius: 4px;
    font-size: 1rem;
}

input[type='text']:focus,
textarea:focus,
select:focus {
    border-color: #5565f7;
    outline: none;
}

/* Button Styling */
button {
    background-color: #3256b0;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-height: 44px; /* Touch-friendly */
}

button:hover {
    background-color: #7c94f7;
    transform: translateY(-1px);
}

button:disabled {
    background-color: #555;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: #5565f7;
    font-weight: bold;
}

.btn-primary:hover {
    background-color: #7c94f7;
}

.btn-secondary {
    background-color: #666;
}

.btn-secondary:hover {
    background-color: #888;
}

.btn-large {
    padding: 12px 20px;
    font-size: 1rem;
    min-height: 44px;
    font-weight: bold;
}

.btn-compact {
    padding: 6px 10px;
    font-size: 0.85rem;
    min-height: 32px;
}

.btn-icon {
    margin-right: 0.5rem;
}

/* Test Profile – enhanced styling */
#test-profile-section {
    border: 1px solid #2a2f44;
    background: #161a24;
    border-left: 5px solid #5565f7;
    box-shadow: 0 4px 14px rgba(0,0,0,0.35);
}

/* Header accent for section */
#test-profile-section h2 {
    color: #9fb2ff;
    border-bottom-color: #2a2f44;
}

/* Selection row panel */
#test-profile-section .profile-selection {
    background: #1b1f2c;
    border: 1px solid #2c3150;
    border-radius: 8px;
    padding: 0.75rem;
}

#test-profile-section .profile-selection label {
    color: #cfe1ff;
    font-size: 0.95rem;
}

#test-profile-section .profile-select {
    background-color: #202434;
    color: #e6e6e6;
    border: 1px solid #39456f;
    border-radius: 6px;
    padding: 0.5rem 0.6rem;
    min-width: 220px;
}

#test-profile-section .profile-select:focus {
    outline: none;
    border-color: #7c94f7;
    box-shadow: 0 0 0 2px rgba(124, 148, 247, 0.25);
}

/* Buttons in selection row */
#test-profile-section #load-profile-details-quick.btn-secondary,
#test-profile-section #manage-profiles-btn.btn-secondary {
    background-color: #4b4f66;
    border: 1px solid #6b73a1;
    color: #e8ebff;
    font-weight: 600;
}
#test-profile-section #load-profile-details-quick.btn-secondary:hover,
#test-profile-section #manage-profiles-btn.btn-secondary:hover {
    background-color: #6b73a1;
}

/* Profile details preview card */
#test-profile-section .profile-details-preview {
    background-color: #151827;
    border: 1px solid #2a2f44;
    border-left: 4px solid #5565f7;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.9rem;
    max-height: 120px;
    color: #dfe6ff;
}

#test-profile-section .profile-details-preview h4 {
    color: #9fb2ff;
    font-size: 0.95rem;
}

/* Test execution controls */
#test-profile-section .test-execution-controls {
    background: #1b1e2b;
    border: 1px solid #2a2f44;
    border-radius: 8px;
    padding: 0.6rem;
    gap: 0.75rem;
}

#test-profile-section .test-execution-controls label {
    color: #cfe1ff;
}

/* Run Tests primary button emphasis */
#test-profile-section #run-tests-btn-quick.btn-primary {
    background: linear-gradient(90deg, #5565f7, #7c94f7);
    border: 1px solid #7c94f788;
    font-weight: 800;
    letter-spacing: 0.2px;
}
#test-profile-section #run-tests-btn-quick.btn-primary:disabled {
    background: #3a3f66;
    border-color: #3a3f66;
    color: #a2a6bf;
}
#test-profile-section #run-tests-btn-quick.btn-primary:hover:not(:disabled) {
    background: linear-gradient(90deg, #6b7df7, #9fb2ff);
    transform: translateY(-1px);
}

/* Responsive tweaks */
@media (max-width: 768px) {
    #test-profile-section .profile-select {
        min-width: auto;
        width: 100%;
    }
}

/* Test Profile Section Styling - Compact */
.test-profile-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex: 1;
}

.profile-selection {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.profile-selection label {
    margin-bottom: 0;
    white-space: nowrap;
    font-size: 0.9rem;
}

.profile-select {
    flex: 1;
    min-width: 160px;
    padding: 8px;
    font-size: 0.9rem;
}

.profile-details-preview {
    background-color: #2a2a2a;
    padding: 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    max-height: 80px;
    overflow-y: auto;
    border-left: 3px solid #5565f7;
    flex: 1;
}

.profile-details-preview:empty {
    display: none;
}

.profile-details-preview h4 {
    margin: 0 0 0.25rem 0;
    color: #5565f7;
    font-size: 0.9rem;
}

.profile-details-preview .profile-info {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 0.25rem 0.75rem;
    align-items: start;
    font-size: 0.8rem;
}

.profile-details-preview .profile-info strong {
    color: #e0e0e0;
}

.test-execution-controls {
    margin-top: auto;
    display: flex;
    justify-content: center;
}

/* Visual Tests Section - Compact */
.visual-tests-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.visual-tests-description {
    font-size: 0.8rem;
    color: #aaa;
    margin: 0;
    text-align: center;
}

.visual-test-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: auto;
}

/* Compact Info Display */
.compact-info {
    font-size: 0.85rem;
    line-height: 1.3;
    flex: 1;
    overflow-y: auto;
}

.compact-info ul {
    margin: 0;
    padding-left: 1rem;
}

.compact-info li {
    margin-bottom: 0.2rem;
}

/* Device Conditions Compact */
.device-conditions-compact {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
}

.status-indicator {
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    background-color: #333;
    min-width: 80px;
    text-align: center;
}

.status-indicator.success {
    background-color: #1e4d1e;
    color: #4CAF50;
}

.status-indicator.warning {
    background-color: #4d3d1e;
    color: #ff9800;
}

/* Profile Management Compact */
.profile-quick-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.profile-select-main {
    flex: 1;
    min-width: 200px;
}

.profile-details-compact {
    background-color: #2a2a2a;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
    max-height: 200px;
    overflow-y: auto;
}

/* Results Viewer Compact */
.results-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.results-alt-input {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.results-alt-input input {
    flex: 1;
    margin-bottom: 0;
}

.results-list-compact {
    max-height: 200px;
    overflow-y: auto;
    background-color: #2a2a2a;
    padding: 1rem;
    border-radius: 4px;
}

/* Test Log Styling */
.test-log {
    background-color: #1a1a1a;
    padding: 1rem;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    border: 1px solid #333;
}

/* Modal Styling */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #2c2c2c;
    padding: 20px;
    border: 1px solid #888;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-large {
    max-width: 800px;
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close-button:hover,
.close-button:focus {
    color: white;
    text-decoration: none;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #444;
}

/* Form Styling */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    max-height: 150px;
    overflow-y: auto;
    padding: 0.5rem;
    background-color: #333;
    border-radius: 4px;
}

.checkbox-grid label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-grid input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Device Conditions Form */
.device-conditions-form {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 0.9rem;
    max-height: 420px;
    overflow-y: auto;
    padding: 0.25rem;
}

/* Category cards with accented left borders similar to sysinfo groups */
.dc-category {
    margin: 0;
    padding: 0.75rem 0.85rem;
    background-color: #1c1f2a;
    border-radius: 8px;
    border: 1px solid #2a2f44;
    border-left-width: 4px;
    border-left-color: #7c94f7; /* default accent; can be rotated per category later */
}

.dc-category h4 {
    margin: 0 0 0.5rem 0;
    color: #9fb2ff;
    font-size: 0.95rem;
    border-bottom: 1px solid #2a2f44;
    padding-bottom: 0.35rem;
    letter-spacing: 0.2px;
}

/* Item rows */
.dc-item {
    display: grid;
    grid-template-columns: 130px 1fr;
    gap: 0.5rem;
    align-items: center;
    margin: 0.25rem 0;
}

.dc-item label {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: #cfe1ff; /* label color for contrast */
    font-weight: 600;
}

.dc-item input,
.dc-item select,
.dc-item textarea {
    margin-bottom: 0;
    background-color: #202434;
    border: 1px solid #39456f;
    color: #e6e6e6;
    border-radius: 6px;
    padding: 0.45rem 0.5rem;
    font-size: 0.9rem;
}

.dc-item select[multiple] {
    min-height: 112px;
}

.dc-item input:focus,
.dc-item select:focus,
.dc-item textarea:focus {
    outline: none;
    border-color: #7c94f7;
    box-shadow: 0 0 0 2px rgba(124, 148, 247, 0.25);
}

/* Notes textarea spans full width */
#dc-input-Notes {
    grid-column: 1 / -1;
    min-height: 80px;
}

/* Actions row below form */
.device-conditions-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.75rem !important;
}

.device-conditions-actions .btn-primary {
    background-color: #4ae287;
    color: #0f1a12;
    font-weight: 700;
}

.device-conditions-actions .btn-primary:hover {
    background-color: #6bf0a0;
}

/* Inline status message adjust for this section */
#dc-status.status-message {
    margin-top: 0;
    padding: 0.4rem 0.6rem;
}

/* Status Messages */
.status-message {
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 1rem;
    text-align: center;
}

.status-message.success {
    background-color: #1e4d1e;
    color: #4CAF50;
}

.status-message.error {
    background-color: #4d1e1e;
    color: #f44336;
}

.status-message.info {
    background-color: #1e3a4d;
    color: #2196F3;
}

/* Logs Section Styling */
.logs-output {
    background-color: #1a1a1a;
    padding: 1rem;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    border: 1px solid #333;
    margin-top: 0.5rem;
}

.logs-output::-webkit-scrollbar {
    width: 8px;
}

.logs-output::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: 4px;
}

.logs-output::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.logs-output::-webkit-scrollbar-thumb:hover {
    background: #666;
}

.logs-placeholder {
    color: #666;
    font-style: italic;
    text-align: center;
    margin: 2rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.log-message {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-left: 3px solid transparent;
    padding-left: 0.5rem;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.log-message.info {
    color: #e0e0e0;
    border-left-color: #2196F3;
}

.log-message.warning {
    color: #ff9800;
    border-left-color: #ff9800;
}

.log-message.error {
    color: #f44336;
    border-left-color: #f44336;
}

.log-message.debug {
    color: #888;
    border-left-color: #666;
}

.log-timestamp {
    color: #666;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.log-level {
    font-weight: bold;
    margin-right: 0.5rem;
    text-transform: uppercase;
    font-size: 0.8rem;
}

.log-message.info .log-level {
    color: #2196F3;
}

.log-message.warning .log-level {
    color: #ff9800;
}

.log-message.error .log-level {
    color: #f44336;
}

.log-message.debug .log-level {
    color: #888;
}

.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    gap: 1rem;
}

/* Result Details */
.result-details-content {
    max-height: 500px;
    overflow-y: auto;
    background-color: #1a1a1a;
    padding: 1rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

footer {
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
    background-color: #1f1f1f;
    color: #aaa;
    border-top: 1px solid #333;
}

/* System Info – improved readability */
.sysinfo {
    font-size: 0.95rem;
}

.sysinfo--error {
    color: #f44336;
    background: rgba(244,67,54,0.08);
    border: 1px solid #f44336;
    padding: 0.75rem;
    border-radius: 6px;
}

.sysinfo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 0.6rem 0.8rem;
}

.sysinfo-group {
    background: #181a22;
    border: 1px solid #2a2f44;
    border-left-width: 4px;
    border-radius: 8px;
    padding: 0.6rem 0.75rem;
}

/* Accent borders per category */
.sysinfo-group--id { border-left-color: #7c94f7; }
.sysinfo-group--cpu { border-left-color: #4a90e2; }
.sysinfo-group--ram { border-left-color: #4ae287; }
.sysinfo-group--gpu { border-left-color: #904ae2; }
.sysinfo-group--display { border-left-color: #90e24a; }
.sysinfo-group--battery { border-left-color: #ff9800; }
.sysinfo-group--disks { border-left-color: #e24a90; }

.sysinfo-row {
    display: grid;
    grid-template-columns: 110px 1fr;
    align-items: start;
    gap: 0.5rem;
}

.sysinfo .k {
    color: #9fb2ff; /* label color */
    font-weight: 700;
    letter-spacing: 0.2px;
}

.sysinfo .v {
    color: #e6e6e6; /* value color */
}

.sysinfo-disklist {
    margin-top: 0.35rem;
    display: grid;
    gap: 0.25rem;
}

.sysinfo-disk {
    display: grid;
    grid-template-columns: 110px 1fr;
    gap: 0.5rem;
}

.chip {
    display: inline-block;
    padding: 0.05rem 0.45rem;
    border-radius: 999px;
    font-size: 0.75rem;
    margin-left: 0.35rem;
    border: 1px solid transparent;
}

.chip--ok {
    color: #4CAF50;
    background: rgba(76,175,80,0.12);
    border-color: rgba(76,175,80,0.35);
}

.chip--bad {
    color: #f44336;
    background: rgba(244,67,54,0.12);
    border-color: rgba(244,67,54,0.35);
}

.chip--unknown {
    color: #ffb74d;
    background: rgba(255,183,77,0.12);
    border-color: rgba(255,183,77,0.35);
}

/* Slightly larger on wide screens for readability */
@media (min-width: 1200px) {
    .sysinfo { font-size: 1rem; }
}
/* System Info Section Styling */
.system-info-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.system-info-section {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.system-info-section h3 {
    margin-top: 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid;
}

.system-info-cpu {
    background-color: #1e2b3a;
    border-left: 4px solid #4a90e2;
}

.system-info-cpu h3 {
    color: #4a90e2;
    border-bottom-color: #4a90e2;
}

.system-info-ram {
    background-color: #1e3a2a;
    border-left: 4px solid #4ae287;
}

.system-info-ram h3 {
    color: #4ae287;
    border-bottom-color: #4ae287;
}

.system-info-disks {
    background-color: #3a1e2b;
    border-left: 4px solid #e24a90;
}

.system-info-disks h3 {
    color: #e24a90;
    border-bottom-color: #e24a90;
}

.system-info-gpus {
    background-color: #2b1e3a;
    border-left: 4px solid #904ae2;
}

.system-info-gpus h3 {
    color: #904ae2;
    border-bottom-color: #904ae2;
}

.system-info-other {
    background-color: #2b3a1e;
    border-left: 4px solid #90e24a;
}

.system-info-other h3 {
    color: #90e24a;
    border-bottom-color: #90e24a;
}

.system-info-item {
    margin: 0.5rem 0;
}

.system-info-item strong {
    color: #ddd;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }

    .secondary-actions {
        grid-template-columns: 1fr 1fr;
    }

    .asset-header {
        gap: 0.75rem;
    }

    .profile-selection {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .secondary-actions {
        grid-template-columns: 1fr;
    }

    .asset-header {
        padding: 0.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .asset-input-group input {
        width: 120px;
    }

    .profile-selection {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .profile-select {
        min-width: auto;
    }

    .profile-quick-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .profile-select-main {
        min-width: auto;
    }

    .results-alt-input {
        flex-direction: column;
        align-items: stretch;
    }

    .visual-test-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-content {
        width: 95%;
        padding: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .dc-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    main {
        padding: 0.5rem;
    }

    .dashboard-card {
        padding: 1rem;
    }

    .asset-header {
        margin-top: 0.5rem;
        padding: 0.5rem;
    }

    .btn-large {
        padding: 12px 20px;
        font-size: 1.1rem;
    }

    h2 {
        font-size: 1.2rem;
    }
}

/* License Banner Styles */
.license-banner {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    border: 1px solid #ffa500;
    border-radius: 8px;
    margin-bottom: 20px;
}

.license-banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.license-info h3 {
    margin: 0 0 8px 0;
    color: #ffa500;
    font-size: 1.2em;
}

.license-info p {
    margin: 0;
    color: #cccccc;
    font-size: 0.95em;
}

.license-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.license-actions .btn-compact {
    white-space: nowrap;
}

@media (max-width: 768px) {
    .license-banner-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .license-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}
