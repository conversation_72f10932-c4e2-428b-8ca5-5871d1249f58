<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/override.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced_results.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced_test_monitor.css') }}">
    <!-- Favicon (data URL to avoid /favicon.ico 404) -->
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64'%3E%3Crect width='64' height='64' fill='%231e1e1e'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='sans-serif' font-size='36' fill='%23ffffff'%3EN%3C/text%3E%3C/svg%3E" />
</head>
<body>
    <header>
        <h1>Crucible</h1>
        <!-- Consolidated Asset Information Header -->
        <div class="asset-header">
            <div class="asset-input-group">
                <label for="operator-id">Operator ID:</label>
                <input type="text" id="operator-id" name="operator-id" placeholder="Enter operator ID" autofocus>
            </div>
            <div class="asset-input-group">
                <label for="asset-number">Asset Number:</label>
                <input type="text" id="asset-number" name="asset-number" placeholder="Enter asset number">
            </div>
            <div class="asset-status" id="asset-status">
                <span id="asset-status-text">Enter asset information to begin</span>
            </div>
        </div>
    </header>
    <main>
        <!-- Main Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Test Profile & Execution -->
            <section id="test-profile-section" class="dashboard-card priority-card">
                <h2>Test Profile</h2>
                <div class="test-profile-content">
                    <div class="profile-selection">
                        <label for="profile-select-quick">Select Profile:</label>
                        <select id="profile-select-quick" name="profile-select-quick" class="profile-select">
                            <option value="">-- Select Profile --</option>
                        </select>
                        <button id="load-profile-details-quick" class="btn-secondary btn-compact">View Details</button>
                        <button id="manage-profiles-btn" class="btn-secondary btn-compact" style="margin-left:8px;">Manage Profiles</button>
                    </div>
                    <div id="profile-details-quick" class="profile-details-preview">
                        <!-- Profile details preview will appear here -->
                    </div>
                    <div class="test-execution-controls">
                            <label style="margin-right: 12px; font-size: 0.9rem;">
                                <input type="checkbox" id="toggle-visual-tests" checked style="margin-right:4px;">Include Visual Tests
                            </label>
                        <button id="run-tests-btn-quick" class="btn-primary" disabled>
                            <span class="btn-icon">▶</span> Run Tests
                        </button>
                    </div>
                </div>
            </section>

            <!-- System Information - Compact -->
            <section id="system-info" class="dashboard-card">
                <h2>System Info</h2>
                <div id="system-info-content" class="compact-info">
                    <p>Loading...</p>
                </div>
            </section>

            <!-- Device Conditions - Integrated -->
            <section id="device-conditions" class="dashboard-card">
                <h2>Device Conditions</h2>
                <div id="dc-form-area" class="device-conditions-form"></div>
                <div class="device-conditions-actions" style="margin-top:8px;">
                    <button id="save-dc-btn" class="btn-primary btn-compact">Save Conditions</button>
                    <span id="dc-status" class="status-message" style="margin-left:12px;"></span>
                </div>
            </section>
        </div>

        <!-- Secondary Actions Row -->
        <div class="secondary-actions">
            <!-- Secure Drive Wipe Section - promoted to top of secondary row -->
            <section id="drive-wipe-section" class="dashboard-card full-width collapsible priority-card">
                <h2 class="collapsible-header" onclick="toggleSection('drive-wipe-section')">
                    Secure Drive Wipe <span class="collapse-icon">▼</span>
                </h2>
                <div class="collapsible-content">
                    <div class="drive-wipe-controls">
                        <button id="load-drives-btn" class="btn-secondary">Load/Refresh Drives</button>
                        <select id="wipe-method-select" name="wipe-method-select" class="wipe-method-select" disabled>
                            <option value="">-- Select Wipe Method --</option>
                        </select>
                        <button id="start-wipe-btn" class="btn-danger" disabled>Start Wipe</button>
                        <button id="cancel-wipe-btn" class="btn-warning" disabled>Cancel Wipe</button>
                    </div>
                    <div id="drive-list-container" class="drive-list-container">
                        <p>Click "Load/Refresh Drives" to see available drives.</p>
                        <!-- Drives will be listed here -->
                    </div>
                    <div class="drive-wipe-status">
                        <h3>Wipe Progress</h3>
                        <div id="wipe-progress-bar-container" class="progress-bar-container">
                            <div id="wipe-progress-bar" class="progress-bar">0%</div>
                        </div>
                        <p id="wipe-status-message">Status: Idle</p>
                        <h4>Wipe Log</h4>
                        <div id="wipe-log-output" class="log-output">
                            <!-- Log messages will appear here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Visual Tests - Collapsible -->
            <section id="visual-tests" class="dashboard-card collapsible">
                <h2 class="collapsible-header" onclick="toggleSection('visual-tests')">
                    Visual Tests <span class="collapse-icon">▼</span>
                </h2>
                <div class="collapsible-content">
                    <div class="visual-tests-content">
                        <p class="visual-tests-description">Individual visual tests for specific hardware components</p>
                        <div class="visual-test-buttons">
                            <button data-test-type="lcd" class="btn-secondary visual-test-link">LCD Display Test</button>
                            <button data-test-type="ram" class="btn-secondary visual-test-link">RAM Test</button>
                            <button data-test-type="cpu" class="btn-secondary visual-test-link">CPU Test</button>
                            <button data-test-type="keyboard" class="btn-secondary visual-test-link">Keyboard Test</button>
                            <button data-test-type="pointing" class="btn-secondary visual-test-link">Pointing Device Test</button>
                            <button data-test-type="touch" class="btn-secondary visual-test-link">Touch Screen Test</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Enhanced Results Viewer -->
            <section id="results-viewer" class="dashboard-card collapsible">
                <h2 class="collapsible-header" onclick="toggleSection('results-viewer')">
                    Results Viewer <span class="collapse-icon">▼</span>
                </h2>
                <div class="collapsible-content">
                    <!-- Enhanced Results Viewer Container -->
                    <div id="enhanced-results-viewer">
                        <!-- Controls -->
                        <div class="results-controls">
                            <div class="view-mode-selector">
                                <button class="view-mode-btn active" data-mode="list">List</button>
                                <button class="view-mode-btn" data-mode="grid">Grid</button>
                                <button class="view-mode-btn" data-mode="timeline">Timeline</button>
                            </div>
                            <div class="results-search-box">
                                <input type="text" id="results-search" placeholder="Search results...">
                                <select id="results-filter">
                                    <option value="all">All Results</option>
                                    <option value="passed">Passed</option>
                                    <option value="failed">Failed</option>
                                    <option value="recent">Last 24h</option>
                                </select>
                            </div>
                            <div class="comparison-toggle">
                                <label>
                                    <input type="checkbox" id="comparison-mode"> Compare Mode
                                </label>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="results-quick-actions">
                            <button id="list-results-btn-quick" class="btn-secondary">View Results for Current Asset</button>
                            <div class="results-alt-input">
                                <input type="text" id="results-asset-number" name="results-asset-number" placeholder="Or enter different asset number">
                                <button id="list-results-btn" class="btn-secondary btn-compact">View</button>
                            </div>
                            <button id="export-results-btn" class="btn-secondary btn-compact">Export</button>
                        </div>
                        
                        <!-- Statistics -->
                        <div id="results-statistics" style="display:none;">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-label">Total Tests</span>
                                    <span class="stat-value" id="stat-total">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Passed</span>
                                    <span class="stat-value text-success" id="stat-passed">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Failed</span>
                                    <span class="stat-value text-danger" id="stat-failed">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Pass Rate</span>
                                    <span class="stat-value" id="stat-rate">0%</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results Container -->
                        <div id="results-container">
                            <div id="results-list" class="results-list-view">
                                <!-- Results will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

        </div>

        <!-- License Status Banner -->
        <section id="license-status-banner" class="dashboard-card full-width license-banner" style="display:block;">
            <div class="license-banner-content">
                <div class="license-info">
                    <h3>🔒 License Required</h3>
                    <p>A valid license is required to run tests and perform drive wipes. Please activate your license to continue.</p>
                </div>
                <div class="license-actions">
                    <button id="check-license-btn" class="btn-primary btn-compact">Check License</button>
                    <button id="purchase-license-btn" class="btn-secondary btn-compact">Purchase License</button>
                    <button id="enter-license-btn" class="btn-secondary btn-compact">Enter License Key</button>
                </div>
            </div>
        </section>

        <!-- Device Conditions Modal -->
        <div id="device-conditions-modal" class="modal" style="display:none;">
            <div class="modal-content modal-large">
                <span class="close-button" onclick="closeDeviceConditionsModal()">&times;</span>
                <h3>Device Conditions Assessment</h3>
                <div class="dc-load-actions" style="margin-bottom:10px;">
                    <label for="dc-asset-number" style="margin-right:6px;">Asset Number:</label>
                    <input type="text" id="dc-asset-number" name="dc-asset-number" placeholder="Enter asset number" style="width:150px; margin-right:6px;">
                    <button id="load-dc-btn" class="btn-secondary btn-compact">Load</button>
                </div>
                <div id="dc-form-area-modal" class="device-conditions-form">
                    <p>Loading device conditions...</p>
                </div>
                <div class="modal-actions">
                    <button id="save-dc-btn" class="btn-primary">Save Conditions</button>
                    <button onclick="closeDeviceConditionsModal()" class="btn-secondary">Cancel</button>
                </div>
                <div id="dc-status" class="status-message"></div>
            </div>
        </div>

        <!-- Profile Management Modal -->
        <div id="profile-modal" class="modal" style="display:none;">
            <div class="modal-content">
                <span class="close-button">&times;</span>
                <h3 id="profile-modal-title">Create Profile</h3>
                <form id="profile-form">
                    <input type="hidden" id="profile-id" name="profile-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-name">Name:</label>
                            <input type="text" id="profile-name" name="profile-name" required>
                        </div>
                        <div class="form-group">
                            <label for="profile-device-type">Device Type:</label>
                            <input type="text" id="profile-device-type" name="profile-device-type" placeholder="e.g., Laptop, Desktop">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="profile-description">Description:</label>
                        <textarea id="profile-description" name="profile-description" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <h4>Tests (select one or more):</h4>
                        <div id="profile-tests-checkboxes" class="checkbox-grid">
                            <!-- Test checkboxes will be populated here by JS -->
                        </div>
                    </div>
                    <!-- RAM Test Configuration Section -->
                    <div class="form-group ram-test-config">
                        <h4>RAM Test Configuration</h4>
                        <div class="ram-config-section" id="ram-config-section" style="display:none;">
                            <div class="size-mode-selection">
                                <label class="radio-label">
                                    <input type="radio" name="ram-size-mode" value="percentage" checked> 
                                    Percentage of Available RAM
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="ram-size-mode" value="absolute"> 
                                    Absolute Size (MB)
                                </label>
                            </div>
                            <div class="ram-size-input">
                                <label for="ram-test-size" title="Test size: 1-50% for percentage mode, or positive integer for MB mode">
                                    Test Size:
                                </label>
                                <input type="number" id="ram-test-size" name="ram-test-size" min="1" max="50" value="25" title="Enter test size (1-50% or MB value)">
                                <span id="ram-size-unit">%</span>
                            </div>
                            <div class="ram-duration-input">
                                <label for="ram-test-duration" title="Test duration in seconds (5-3600)">
                                    Duration:
                                </label>
                                <input type="number" id="ram-test-duration" name="ram-test-duration" min="5" max="3600" value="30" title="Test duration in seconds">
                                <span>seconds</span>
                            </div>
                            <div class="ram-config-preview" id="ram-config-preview">
                                <!-- Dynamic preview of calculated values -->
                            </div>
                            <div class="ram-config-help">
                                <small>
                                    <strong>Percentage mode:</strong> Recommended for profiles used across different devices (1-50%).<br>
                                    <strong>Absolute mode:</strong> Fixed MB amount, useful for specific hardware configurations.<br>
                                    <strong>Duration:</strong> How long the RAM test should run (5-3600 seconds).
                                </small>
                            </div>
                        </div>
                    </div>
                    <!-- CPU Test Configuration Section -->
                    <div class="form-group cpu-test-config">
                        <h4>CPU Test Configuration</h4>
                        <div class="cpu-config-section" id="cpu-config-section" style="display:none;">
                            <div class="cpu-duration-input">
                                <label for="cpu-test-duration" title="Test duration in seconds (5-300)">
                                    Test Duration:
                                </label>
                                <input type="number" id="cpu-test-duration" name="cpu-test-duration" min="5" max="300" value="20" title="Enter test duration (5-300 seconds)">
                                <span>seconds</span>
                                <small class="help-text">Recommended: 20-60 seconds for most devices</small>
                            </div>
                            <div class="cpu-intensity-input">
                                <label for="cpu-load-intensity" title="CPU load intensity percentage (50-100%)">
                                    CPU Load Intensity:
                                </label>
                                <input type="range" id="cpu-load-intensity" name="cpu-load-intensity" min="50" max="100" value="100" title="CPU load intensity (50-100%)">
                                <span id="cpu-intensity-value">100%</span>
                                <small class="help-text">Higher values provide more thorough testing</small>
                            </div>
                            <div class="cpu-config-preview" id="cpu-config-preview">
                                <!-- Dynamic preview of configured values -->
                            </div>
                            <div class="cpu-config-help">
                                <small>
                                    <strong>Duration:</strong> How long the CPU test should run (5-300 seconds).<br>
                                    <strong>Intensity:</strong> CPU load percentage for stress testing (50-100%).<br>
                                    <strong>Recommended settings:</strong> Desktop: 30s/100%, Laptop: 20s/85%
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <h4>Test Arguments (JSON format):</h4>
                        <textarea id="profile-test-args" name="profile-test-args" rows="4" placeholder='{"test_name_1": {"arg1": "value1"}, "test_name_2": {}}'></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="submit" id="save-profile-btn" class="btn-primary">Save Profile</button>
                        <button type="button" onclick="closeProfileModal()" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Detail View Modal -->
        <div id="result-details-modal" class="modal" style="display:none;">
            <div class="modal-content modal-large">
                <span class="close-button" onclick="closeResultDetailsModal()">&times;</span>
                <h3 id="result-details-title">Result Details</h3>
                <div id="result-details-view" class="result-details-content">
                    <!-- Content of a selected result file will appear here -->
                </div>
                <div class="modal-actions">
                    <button onclick="closeResultDetailsModal()" class="btn-secondary">Close</button>
                </div>
            </div>
        </div>
        
        <!-- Enhanced Result Details Modal -->
        <div id="enhanced-result-details-modal" class="modal" style="display:none;">
            <div class="modal-content modal-large">
                <div class="modal-header">
                    <h3 id="enhanced-result-details-title">Result Details</h3>
                    <span class="close-button" onclick="window.enhancedResultsViewer.closeDetailsModal()">&times;</span>
                </div>
                <div id="enhanced-result-details-content">
                    <!-- Enhanced result details will appear here -->
                </div>
            </div>
        </div>

        <!-- License Key Entry Modal -->
        <div id="license-key-modal" class="modal" style="display:none;">
            <div class="modal-content">
                <span class="close-button" onclick="closeLicenseKeyModal()">&times;</span>
                <h3>Enter License Key</h3>
                <div class="form-group">
                    <label for="license-key-input">License Key:</label>
                    <input type="text" id="license-key-input" placeholder="XXXX-XXXX-XXXX-XXXX" maxlength="19" style="text-transform: uppercase;">
                    <small style="color: #aaa; display: block; margin-top: 5px;">
                        Enter your license key in the format: XXXX-XXXX-XXXX-XXXX
                    </small>
                </div>
                <div id="license-validation-message" class="status-message" style="display:none;"></div>
                <div class="modal-actions">
                    <button id="validate-license-btn" class="btn-primary">Validate License</button>
                    <button onclick="closeLicenseKeyModal()" class="btn-secondary">Cancel</button>
                </div>
            </div>
        </div>

    </main>
    <footer>
        <p>&copy; 2025 Aaron Paulina | Version {{ version }}</p>
    </footer>
    <script type="module" src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script type="module" src="{{ url_for('static', filename='js/modules/enhanced_results_viewer.js') }}"></script>
    <script type="module" src="{{ url_for('static', filename='js/modules/enhanced_test_monitor.js') }}"></script>
</body>
</html>
